import React from 'react';
import {
  Pressable,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { row, contentWidth, shadowSmall } from '~/styles/views';
import { buttonText, blackishText, grayishText } from '~/styles/text';
import colors from '~/styles/colors';

interface ProfileActionButtonProps {
  primaryText: string;
  secondaryText?: string;
  icon: React.ReactNode;
  onPress: () => void;
  containerStyle?: ViewStyle;
  primaryTextStyle?: TextStyle;
  secondaryTextStyle?: TextStyle;
  disabled?: boolean;
}

const ProfileActionButton = ({
  primaryText,
  secondaryText,
  icon,
  onPress,
  containerStyle,
  primaryTextStyle,
  secondaryTextStyle,
  disabled = false,
}: ProfileActionButtonProps) => {
  return (
    <Pressable onPress={onPress} disabled={disabled}>
      <View style={[styles.container, row, containerStyle] as ViewStyle}>
        <Text style={[styles.primaryText, primaryTextStyle] as TextStyle}>
          {primaryText}
        </Text>

        {secondaryText && (
          <Text style={[styles.secondaryText, secondaryTextStyle] as TextStyle}>
            {secondaryText}
          </Text>
        )}

        {icon}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    width: contentWidth,
    borderRadius: 16,
    backgroundColor: colors.white,
    ...shadowSmall,
    justifyContent: 'space-between',
    alignItems: 'center',
  } as ViewStyle,
  primaryText: {
    ...buttonText,
    ...blackishText,
  } as TextStyle,
  secondaryText: {
    ...grayishText,
    fontSize: 18,
    textAlign: 'right',
    color: colors.grey900,
    flex: 1,
    marginRight: 16,
    marginLeft: 8,
  } as TextStyle,
});

export default ProfileActionButton;
