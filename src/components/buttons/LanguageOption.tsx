import React from 'react';
import { View, Text, Pressable, TextStyle } from 'react-native';
import { SvgProps } from 'react-native-svg';
import { Checkmark } from '~/components/icons';
import colors from '~/styles/colors';
import { cardPrimaryText, cardSecondaryText } from '~/styles/text';
import {
  cardBase,
  cardContentContainer,
  cardIconContainer,
  cardRightActionContainer,
} from '~/styles/views';

interface LanguageOptionProps {
  languageName: string;
  secondaryText?: string;
  FlagComponent: React.FC<SvgProps>;
  isSelected?: boolean;
  onPress?: () => void;
}

const LanguageOption = ({
  languageName,
  secondaryText,
  FlagComponent,
  isSelected = false,
  onPress,
}: LanguageOptionProps) => {
  return (
    <Pressable
      onPress={onPress}
      style={cardBase}
      testID="language-option"
      accessibilityRole="button"
      accessibilityState={{ selected: isSelected }}
      accessibilityLabel={`${languageName} Selected`}>
      <FlagComponent style={cardIconContainer} />
      <View style={cardContentContainer}>
        <Text style={cardPrimaryText as TextStyle}>{languageName}</Text>
        {secondaryText && (
          <Text style={cardSecondaryText as TextStyle}>{secondaryText}</Text>
        )}
      </View>
      <View style={cardRightActionContainer}>
        {isSelected && <Checkmark color={colors.greenDark} size={32} />}
      </View>
    </Pressable>
  );
};

export default LanguageOption;
