import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from 'react-native';
import BottomSheetModal from '~/components/modals/BottomSheetModal';
import { callPhoneNumber } from '~/services/dispatch/CallService';
import { sendSMS } from '~/services/dispatch/SmsService';
import colors from '~/styles/colors';
import { marginBottom10 } from '~/styles/spacing';
import { buttonText, centerText } from '~/styles/text';
import { center, deviceWidth } from '~/styles/views';

/**
 * The Contact Dispatch component opens up a modal with <PERSON><PERSON><PERSON><PERSON>'s phone number with call and sms functionality.
 *
 * @component
 * @param {boolean} props.isVisible - This is variable that maintains the state of modal whether it is open or close.
 * @param {function} props.onClose - This is the function that gets called when the user clicks on the cancel button or click anywhere in the background of the modal.

 * @example
 * // Example usage of ContactDispatch
 * <ContactDispatch
 *    isVisible={showContactOptionsDialog}
 *    onClose={close}
 * />
 */

const ContactDispatch = ({ isVisible, onClose }: any) => {
  const renderActionButtons = (onButtonPress: () => void, title: string) => {
    return (
      <TouchableOpacity style={styles.button} onPress={onButtonPress}>
        <Text style={styles.buttonText}>{title}</Text>
      </TouchableOpacity>
    );
  };
  return (
    <BottomSheetModal isVisible={isVisible} onClose={onClose}>
      <View style={styles.container}>
        {renderActionButtons(
          () => sendSMS('****** 675 0477'),
          'Send Message ****** 675 0477',
        )}
        {renderActionButtons(
          () => callPhoneNumber('****** 341 9640'),
          'Call ****** 341 9640',
        )}
        {renderActionButtons(onClose, 'Cancel')}
      </View>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  container: {
    ...center,
    marginBottom: 20,
  } as ViewStyle,
  button: {
    ...center,
    width: deviceWidth - 40,
    height: 65,
    borderRadius: 20,
    backgroundColor: colors.grayishBlue,
    ...marginBottom10,
  } as ViewStyle,
  buttonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
  } as TextStyle,
});

export default ContactDispatch;
