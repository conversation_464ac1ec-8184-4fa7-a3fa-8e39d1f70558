import React, { useEffect, useState } from 'react';
import { Image, StyleProp, ImageStyle } from 'react-native';
import axios, { AxiosRequestConfig } from 'axios';
import TokenService from '~/services/TokenService';

interface SalesforceImageProps {
  imageUrl: string;
  style?: StyleProp<ImageStyle>;
}

const defaultPfp = require('~/assets/images/default_pfp.webp');

const SalesforceImage: React.FC<SalesforceImageProps> = ({
  imageUrl,
  style,
}) => {
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const accessToken = await TokenService.getAccessToken();
        if (!accessToken) {
          throw new Error(
            'SalesforceImage.tsx: fetchImage(): No access token available',
          );
        }

        const config: AxiosRequestConfig = {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          responseType: 'blob',
        };

        const response = await axios.get(imageUrl, config);

        const blob = response.data;
        const reader = new FileReader();
        reader.onloadend = () => {
          setBase64Image(reader.result as string);
        };
        reader.readAsDataURL(blob);
      } catch (error) {
        console.error('SalesforceImage.tsx: fetchImage(): ', error);
        setHasError(true);
      }
    };

    fetchImage();
  }, [imageUrl]);

  if (hasError) {
    return <Image source={defaultPfp} style={style} />;
  }

  if (!base64Image) return null;

  return <Image source={{ uri: base64Image }} style={style} />;
};

export default SalesforceImage;
