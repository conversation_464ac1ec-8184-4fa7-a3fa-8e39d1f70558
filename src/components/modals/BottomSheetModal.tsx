import React from 'react';
import { View, StyleSheet } from 'react-native';
import Modal from 'react-native-modal';

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with customizable options.
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed. Default is () => {}.
 * @param {React.ReactNode} props.children - Content displayed below the BlurView. Optional.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  children,
}: BottomSheetModalProps) => {
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      {children && <View>{children}</View>}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
});

export default BottomSheetModal;
