import React, { useState } from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import Title from '~/components/text/Title';
import * as locales from '~/localization';
import ScreenWrapper from '~/screens/ScreenWrapper';
import colors from '~/styles/colors';
import LanguageOption from '~/components/buttons/LanguageOption';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';

// TODO: Update app language based off of selected language
const LanguageSelectionScreen = () => {
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const localeEntries = Object.entries(locales) as Array<
    [keyof typeof locales, (typeof locales)[keyof typeof locales]]
  >;

  const bottomTabBarHeight = useBottomTabBarHeight();
  const scrollContainerStyle = {
    paddingBottom: bottomTabBarHeight,
  };

  return (
    <ScreenWrapper color={colors.white}>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView
          contentContainerStyle={scrollContainerStyle}
          showsVerticalScrollIndicator={false}>
          <View style={styles.container}>
            <Title
              title={locales.en.select_language}
              subtitle={locales.en.language_information}
            />

            <View>
              {localeEntries.map(([code, language]) => (
                <LanguageOption
                  key={code}
                  languageName={language.language_name}
                  secondaryText={language.language_english}
                  FlagComponent={language.FlagComponent}
                  isSelected={selectedLanguage === code}
                  onPress={() => setSelectedLanguage(code)}
                />
              ))}
            </View>
          </View>
        </ScrollView>
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
    padding: 16,
    flex: 1,
    backgroundColor: colors.white,
    gap: 12,
  },
});

export default LanguageSelectionScreen;
