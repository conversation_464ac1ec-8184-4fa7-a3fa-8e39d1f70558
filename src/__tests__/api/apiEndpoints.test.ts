import { API_ENDPOINT_KEYS, API_ENDPOINTS } from '~/api/apiEndpoints';

const {
  GET_ROUTE_LIST,
  GET_STOP_LIST,
  GET_CONTACT_BY_USER_ID,
  POST_HEARTBEAT_LOCATION,
  POST_BATCH_SOBJECTS,
} = API_ENDPOINT_KEYS;

describe('API Endpoints', () => {
  test('should export correct constant values', () => {
    expect(GET_ROUTE_LIST).toBe('getRouteList');
    expect(GET_STOP_LIST).toBe('getStopList');
    expect(GET_CONTACT_BY_USER_ID).toBe('getContactByUserId');
    expect(POST_HEARTBEAT_LOCATION).toBe('postHeartbeatLocation');
    expect(POST_BATCH_SOBJECTS).toBe('postBatchSobjects');
  });

  test('should generate correct SOQL query for getRouteList', () => {
    const expectedQuery =
      'query?q=select+LastModifiedDate%2CId%2CName%2CPlanned_Start__c%2CPlanned_End__c%2CDate__c%2CNumber_of_Stops__c%2CStatus__c%2CRoute__r.Name%2CSNO_Flow__c%2CUTC_Offset__c%2CRapid_Closure_Warning__c%2CDefault_Delivery_Destination__c%2CSurvey_Complete__c%2CRequire_End_of_Route_Survey__c%2CGeofencing_Distance__c%2CHigh_Temperature__c%2CLock_Check_In_Order__c+from+Route_Summary__c+where+Date__c+%3E%3D+LAST_N_DAYS%3A1+order+by+LastModifiedDate+limit+200';
    expect(API_ENDPOINTS[GET_ROUTE_LIST]).toBe(expectedQuery);
  });

  test('should have getStopList endpoint defined', () => {
    // The getStopList endpoint is not currently implemented in the API_ENDPOINTS
    // This test should be updated when the endpoint is added
    expect(API_ENDPOINTS[GET_STOP_LIST]).toBeUndefined();
  });

  test('should have correct endpoint for getContactById', () => {
    const expectedQuery =
      'query?q=select+Id%2CName%2CEmail%2COwnerId%2CAccountId%2CSalesforce_Account__c%2CSalesforce_Account__r.MediumPhotoUrl%2CSalesforce_Account__r.LanguageLocaleKey+from+Contact+where+Salesforce_Account__c+%3D+%27{userId}%27+order+by+LastModifiedDate+limit+200';
    expect(API_ENDPOINTS[GET_CONTACT_BY_USER_ID]).toBe(expectedQuery);
  });

  test('should have correct endpoint for postHeartbeatLocation', () => {
    expect(API_ENDPOINTS[POST_HEARTBEAT_LOCATION]).toBe(
      'sobjects/Location_History__c',
    );
  });

  test('should have correct endpoint for postBatchSobjects', () => {
    expect(API_ENDPOINTS[POST_BATCH_SOBJECTS]).toBe('composite/sobjects/');
  });

  describe('generateSoqlQuery', () => {
    test('should include all required query components', () => {
      const query = API_ENDPOINTS[GET_ROUTE_LIST];
      expect(query).toContain('select+');
      expect(query).toContain('from+Route_Summary__c');
      expect(query).toContain('where+');
      expect(query).toContain('order+by+');
      expect(query).toContain('limit+200');
    });

    test('should properly encode special characters', () => {
      const query = API_ENDPOINTS[GET_ROUTE_LIST];
      expect(query).toContain('%2C');
      expect(query).toContain('%3E%3D');
    });
  });

  test('should have all required endpoints defined', () => {
    const requiredEndpoints = [
      GET_ROUTE_LIST,
      GET_CONTACT_BY_USER_ID,
      POST_HEARTBEAT_LOCATION,
      POST_BATCH_SOBJECTS,
    ];

    requiredEndpoints.forEach(endpoint => {
      expect(API_ENDPOINTS).toHaveProperty(endpoint);
      expect(typeof API_ENDPOINTS[endpoint]).toBe('string');
      expect(API_ENDPOINTS[endpoint].length).toBeGreaterThan(0);
    });
  });

  test('should have getStopList endpoint key defined but not implemented', () => {
    // The key exists in API_ENDPOINT_KEYS but the endpoint is not implemented yet
    expect(API_ENDPOINT_KEYS).toHaveProperty('GET_STOP_LIST');
    expect(API_ENDPOINT_KEYS.GET_STOP_LIST).toBe('getStopList');
    expect(API_ENDPOINTS[GET_STOP_LIST]).toBeUndefined();
  });
});
