// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LanguageOption matches snapshot when not selected 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "backgroundColor": "#FFFFFF",
      "borderRadius": 12,
      "elevation": 16,
      "flexDirection": "row",
      "justifyContent": "center",
      "marginBottom": 16,
      "padding": 16,
      "shadowColor": "#000000",
      "shadowOffset": {
        "height": 4,
        "width": 0,
      },
      "shadowOpacity": 0.1,
      "shadowRadius": 16,
    }
  }
  testID="language-option"
>
  <View
    style={
      {
        "borderRadius": 2,
        "height": 20,
        "marginRight": 16,
        "width": 33,
      }
    }
    testID="flag-component"
  />
  <View
    style={
      {
        "flex": 1,
        "rowGap": 4,
      }
    }
  >
    <Text
      style={
        {
          "color": "#191919",
          "fontFamily": "Satoshi Variable",
          "fontSize": 16,
          "fontWeight": "500",
        }
      }
    >
      Test Language
    </Text>
    <Text
      style={
        {
          "color": "#766c6c",
          "fontFamily": "Satoshi Variable",
          "fontSize": 14,
        }
      }
    >
      Secondary Text
    </Text>
  </View>
  <View
    style={
      {
        "marginLeft": 12,
      }
    }
  />
</View>
`;

exports[`LanguageOption matches snapshot when selected 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "backgroundColor": "#FFFFFF",
      "borderRadius": 12,
      "elevation": 16,
      "flexDirection": "row",
      "justifyContent": "center",
      "marginBottom": 16,
      "padding": 16,
      "shadowColor": "#000000",
      "shadowOffset": {
        "height": 4,
        "width": 0,
      },
      "shadowOpacity": 0.1,
      "shadowRadius": 16,
    }
  }
  testID="language-option"
>
  <View
    style={
      {
        "borderRadius": 2,
        "height": 20,
        "marginRight": 16,
        "width": 33,
      }
    }
    testID="flag-component"
  />
  <View
    style={
      {
        "flex": 1,
        "rowGap": 4,
      }
    }
  >
    <Text
      style={
        {
          "color": "#191919",
          "fontFamily": "Satoshi Variable",
          "fontSize": 16,
          "fontWeight": "500",
        }
      }
    >
      Test Language
    </Text>
    <Text
      style={
        {
          "color": "#766c6c",
          "fontFamily": "Satoshi Variable",
          "fontSize": 14,
        }
      }
    >
      Secondary Text
    </Text>
  </View>
  <View
    style={
      {
        "marginLeft": 12,
      }
    }
  >
    <View
      color="#367D5F"
      size={32}
      testID="checkmark"
    />
  </View>
</View>
`;
