import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import LanguageOption from '~/components/buttons/LanguageOption';
import colors from '~/styles/colors';

jest.mock('~/components/icons', () => {
  const React = require('react');
  return {
    Checkmark: (props: any) =>
      React.createElement('View', { ...props, testID: 'checkmark' }),
  };
});

// Mock flag component for testing
const MockFlagComponent = (props: any) => {
  const React = require('react');
  return React.createElement('View', { ...props, testID: 'flag-component' });
};

describe('LanguageOption', () => {
  const onPressMock = jest.fn();
  const baseProps = {
    languageName: 'Test Language',
    secondaryText: 'Secondary Text',
    FlagComponent: MockFlagComponent,
    isSelected: false,
    onPress: onPressMock,
  };

  beforeEach(() => {
    onPressMock.mockClear();
  });

  it('renders the primary and optional secondary texts', () => {
    const { getByText } = render(<LanguageOption {...baseProps} />);
    expect(getByText('Test Language')).toBeTruthy();
    expect(getByText('Secondary Text')).toBeTruthy();
  });

  it('omits secondary text when none is provided', () => {
    const { queryByText } = render(
      <LanguageOption {...baseProps} secondaryText={undefined} />,
    );
    expect(queryByText('Secondary Text')).toBeNull();
  });

  it('renders the flag SVG component', () => {
    const { getByTestId } = render(<LanguageOption {...baseProps} />);
    expect(getByTestId('flag-component')).toBeTruthy();
    expect(getByTestId('language-option')).toBeTruthy();
  });

  it('calls onPress when primary text is pressed', () => {
    const { getByText } = render(<LanguageOption {...baseProps} />);
    fireEvent.press(getByText('Test Language'));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });

  it('calls onPress when pressing the container', () => {
    const { getByTestId } = render(<LanguageOption {...baseProps} />);
    fireEvent.press(getByTestId('language-option'));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });

  it('calls onPress when pressing the flag area', () => {
    const { getByTestId } = render(<LanguageOption {...baseProps} />);
    fireEvent.press(getByTestId('language-option'));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });

  it('does not throw when onPress is not provided', () => {
    const { getByTestId } = render(
      <LanguageOption {...baseProps} onPress={undefined} />,
    );
    expect(() => {
      fireEvent.press(getByTestId('language-option'));
    }).not.toThrow();
  });

  it('does not show the checkmark when isSelected is false', () => {
    const { queryByTestId } = render(
      <LanguageOption {...baseProps} isSelected={false} />,
    );
    expect(queryByTestId('checkmark')).toBeNull();
  });

  it('shows the checkmark when isSelected is true', () => {
    const { getByTestId } = render(
      <LanguageOption {...baseProps} isSelected={true} />,
    );
    expect(getByTestId('checkmark')).toBeTruthy();
  });

  it('does not show checkmark by default when isSelected prop is omitted', () => {
    const { queryByTestId } = render(<LanguageOption {...baseProps} />);
    expect(queryByTestId('checkmark')).toBeNull();
  });

  it('passes correct props to Checkmark icon when selected', () => {
    const { getByTestId } = render(
      <LanguageOption {...baseProps} isSelected />,
    );
    const checkIcon = getByTestId('checkmark');
    expect(checkIcon.props.color).toBe(colors.greenDark);
    expect(checkIcon.props.size).toBe(32);
  });

  it('toggles checkmark on rerender when isSelected prop changes', () => {
    const { queryByTestId, rerender } = render(
      <LanguageOption {...baseProps} isSelected={false} />,
    );
    expect(queryByTestId('checkmark')).toBeNull();
    rerender(<LanguageOption {...baseProps} isSelected />);
    expect(queryByTestId('checkmark')).toBeTruthy();
  });

  it('matches snapshot when not selected', () => {
    const tree = render(<LanguageOption {...baseProps} />).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('matches snapshot when selected', () => {
    const tree = render(
      <LanguageOption {...baseProps} isSelected={true} />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('renders long language names correctly', () => {
    const longName = 'L'.repeat(100);
    const { getByText } = render(
      <LanguageOption {...baseProps} languageName={longName} />,
    );
    expect(getByText(longName)).toBeTruthy();
  });
});
