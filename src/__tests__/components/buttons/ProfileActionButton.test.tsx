import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Text } from 'react-native';
import ProfileActionButton from '~/components/buttons/ProfileActionButton';
import { Arrow, Exit } from '~/components/icons';
import colors from '~/styles/colors';

// Mock the icon components
jest.mock('~/components/icons', () => ({
  Arrow: ({ color, testID }: { color?: string; testID?: string }) => {
    const React = require('react');
    return React.createElement(
      'Text',
      { testID: testID || 'arrow-icon' },
      `Arrow-${color}`,
    );
  },
  Exit: ({ testID }: { testID?: string }) => {
    const React = require('react');
    return React.createElement(
      'Text',
      { testID: testID || 'exit-icon' },
      'Exit',
    );
  },
}));

// Mock the style imports
jest.mock('~/styles/views', () => ({
  row: { flexDirection: 'row' },
  contentWidth: '100%',
  shadowSmall: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
}));

jest.mock('~/styles/text', () => ({
  buttonText: { fontSize: 16, fontWeight: '600' },
  blackishText: { color: '#333' },
  grayishText: { color: '#666' },
}));

jest.mock('~/styles/colors', () => ({
  white: '#FFFFFF',
  grey900: '#111827',
  red600: '#DC2626',
}));

// Mock icon components for unit tests
const MockIcon = ({ testID }: { testID?: string }) => (
  <Text testID={testID || 'mock-icon'}>Icon</Text>
);

describe('ProfileActionButton', () => {
  const defaultProps = {
    primaryText: 'Test Button',
    icon: <MockIcon testID="test-icon" />,
    onPress: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders correctly with required props', () => {
      const { getByText, getByTestId } = render(
        <ProfileActionButton {...defaultProps} />,
      );

      expect(getByText('Test Button')).toBeTruthy();
      expect(getByTestId('test-icon')).toBeTruthy();
    });

    it('renders primary text correctly', () => {
      const { getByText } = render(
        <ProfileActionButton
          {...defaultProps}
          primaryText="Custom Primary Text"
        />,
      );

      expect(getByText('Custom Primary Text')).toBeTruthy();
    });

    it('renders icon correctly', () => {
      const customIcon = <MockIcon testID="custom-icon" />;
      const { getByTestId } = render(
        <ProfileActionButton {...defaultProps} icon={customIcon} />,
      );

      expect(getByTestId('custom-icon')).toBeTruthy();
    });

    it('renders without secondary text when not provided', () => {
      const { queryByText } = render(<ProfileActionButton {...defaultProps} />);

      // Should not render any secondary text
      expect(queryByText('Secondary')).toBeNull();
    });

    it('renders with secondary text when provided', () => {
      const { getByText } = render(
        <ProfileActionButton
          {...defaultProps}
          secondaryText="Secondary Text"
        />,
      );

      expect(getByText('Test Button')).toBeTruthy();
      expect(getByText('Secondary Text')).toBeTruthy();
    });
  });

  describe('Interaction', () => {
    it('calls onPress when button is pressed', () => {
      const mockOnPress = jest.fn();
      const { getByText } = render(
        <ProfileActionButton {...defaultProps} onPress={mockOnPress} />,
      );

      fireEvent.press(getByText('Test Button'));
      expect(mockOnPress).toHaveBeenCalledTimes(1);
    });

    it('does not call onPress when button is disabled', () => {
      const mockOnPress = jest.fn();
      const { getByText } = render(
        <ProfileActionButton
          {...defaultProps}
          onPress={mockOnPress}
          disabled={true}
        />,
      );

      fireEvent.press(getByText('Test Button'));
      expect(mockOnPress).not.toHaveBeenCalled();
    });

    it('handles multiple presses correctly', () => {
      const mockOnPress = jest.fn();
      const { getByText } = render(
        <ProfileActionButton {...defaultProps} onPress={mockOnPress} />,
      );

      const button = getByText('Test Button');
      fireEvent.press(button);
      fireEvent.press(button);
      fireEvent.press(button);

      expect(mockOnPress).toHaveBeenCalledTimes(3);
    });
  });

  describe('Styling', () => {
    it('applies custom container style', () => {
      const customStyle = { backgroundColor: '#FF0000' };
      const { getByText } = render(
        <ProfileActionButton {...defaultProps} containerStyle={customStyle} />,
      );

      const button = getByText('Test Button').parent?.parent;
      expect(button).toHaveStyle(customStyle);
    });

    it('applies custom primary text style', () => {
      const customTextStyle = { color: '#FF0000', fontSize: 20 };
      const { getByText } = render(
        <ProfileActionButton
          {...defaultProps}
          primaryTextStyle={customTextStyle}
        />,
      );

      const primaryText = getByText('Test Button');
      expect(primaryText).toHaveStyle(customTextStyle);
    });

    it('applies custom secondary text style', () => {
      const customSecondaryStyle = { color: '#00FF00', fontWeight: 'bold' };
      const { getByText } = render(
        <ProfileActionButton
          {...defaultProps}
          secondaryText="Secondary"
          secondaryTextStyle={customSecondaryStyle}
        />,
      );

      const secondaryText = getByText('Secondary');
      expect(secondaryText).toHaveStyle(customSecondaryStyle);
    });
  });

  describe('Props validation', () => {
    it('handles disabled prop correctly', () => {
      const { rerender, getByText } = render(
        <ProfileActionButton {...defaultProps} disabled={false} />,
      );

      let button = getByText('Test Button').parent?.parent;
      expect(button).not.toBeDisabled();

      rerender(<ProfileActionButton {...defaultProps} disabled={true} />);

      button = getByText('Test Button').parent?.parent;
      expect(button).toBeDisabled();
    });

    it('defaults disabled to false when not provided', () => {
      const { getByText } = render(<ProfileActionButton {...defaultProps} />);

      const button = getByText('Test Button').parent?.parent;
      expect(button).not.toBeDisabled();
    });
  });

  describe('Layout combinations', () => {
    it('renders correctly with all optional props', () => {
      const { getByText, getByTestId } = render(
        <ProfileActionButton
          primaryText="Primary"
          secondaryText="Secondary"
          icon={<MockIcon testID="full-icon" />}
          onPress={jest.fn()}
          containerStyle={{ padding: 20 }}
          primaryTextStyle={{ fontSize: 18 }}
          secondaryTextStyle={{ fontSize: 14 }}
          disabled={false}
        />,
      );

      expect(getByText('Primary')).toBeTruthy();
      expect(getByText('Secondary')).toBeTruthy();
      expect(getByTestId('full-icon')).toBeTruthy();
    });

    it('renders correctly with minimal props', () => {
      const { getByText, getByTestId } = render(
        <ProfileActionButton
          primaryText="Minimal"
          icon={<MockIcon testID="minimal-icon" />}
          onPress={jest.fn()}
        />,
      );

      expect(getByText('Minimal')).toBeTruthy();
      expect(getByTestId('minimal-icon')).toBeTruthy();
    });
  });

  describe('Integration Tests - Real-world Usage', () => {
    describe('Language Selection Button', () => {
      it('renders language button as used in ProfileScreen', () => {
        const mockNavigate = jest.fn();
        const { getByText, getByTestId } = render(
          <ProfileActionButton
            primaryText="Language"
            secondaryText="English"
            icon={<Arrow color={colors.red600} testID="language-arrow" />}
            onPress={() => mockNavigate('LanguageSelection')}
          />,
        );

        expect(getByText('Language')).toBeTruthy();
        expect(getByText('English')).toBeTruthy();
        expect(getByTestId('language-arrow')).toBeTruthy();
        expect(getByTestId('language-arrow')).toHaveTextContent(
          'Arrow-#DC2626',
        );
      });

      it('handles language button press correctly', () => {
        const mockNavigate = jest.fn();
        const { getByText } = render(
          <ProfileActionButton
            primaryText="Language"
            secondaryText="English"
            icon={<Arrow color={colors.red600} />}
            onPress={() => mockNavigate('LanguageSelection')}
          />,
        );

        fireEvent.press(getByText('Language'));
        expect(mockNavigate).toHaveBeenCalledWith('LanguageSelection');
      });

      it('displays correct secondary text for different languages', () => {
        const languages = ['English', 'Spanish', 'French', 'German'];

        languages.forEach(language => {
          const { getByText, rerender } = render(
            <ProfileActionButton
              primaryText="Language"
              secondaryText={language}
              icon={<Arrow color={colors.red600} />}
              onPress={jest.fn()}
            />,
          );

          expect(getByText(language)).toBeTruthy();

          // Clean up for next iteration
          rerender(<></>);
        });
      });
    });

    describe('Logout Button', () => {
      it('renders logout button as used in ProfileScreen', () => {
        const mockOpenLogoutModal = jest.fn();
        const { getByText, getByTestId } = render(
          <ProfileActionButton
            primaryText="Logout"
            icon={<Exit testID="logout-exit" />}
            onPress={() => mockOpenLogoutModal()}
          />,
        );

        expect(getByText('Logout')).toBeTruthy();
        expect(getByTestId('logout-exit')).toBeTruthy();
        expect(getByTestId('logout-exit')).toHaveTextContent('Exit');
      });

      it('handles logout button press correctly', () => {
        const mockOpenLogoutModal = jest.fn();
        const { getByText } = render(
          <ProfileActionButton
            primaryText="Logout"
            icon={<Exit />}
            onPress={() => mockOpenLogoutModal()}
          />,
        );

        fireEvent.press(getByText('Logout'));
        expect(mockOpenLogoutModal).toHaveBeenCalledTimes(1);
      });

      it('does not render secondary text for logout button', () => {
        const { queryByText } = render(
          <ProfileActionButton
            primaryText="Logout"
            icon={<Exit />}
            onPress={jest.fn()}
          />,
        );

        // Should not have any secondary text
        expect(queryByText('English')).toBeNull();
        expect(queryByText('Secondary')).toBeNull();
      });
    });

    describe('Real-world Usage Scenarios', () => {
      it('handles rapid successive presses gracefully', () => {
        const mockOnPress = jest.fn();
        const { getByText } = render(
          <ProfileActionButton
            primaryText="Test Button"
            icon={<Exit />}
            onPress={mockOnPress}
          />,
        );

        const button = getByText('Test Button');

        // Simulate rapid presses
        for (let i = 0; i < 5; i++) {
          fireEvent.press(button);
        }

        expect(mockOnPress).toHaveBeenCalledTimes(5);
      });

      it('maintains accessibility for screen readers', () => {
        const { getByText } = render(
          <ProfileActionButton
            primaryText="Accessible Button"
            secondaryText="Secondary Info"
            icon={<Arrow color={colors.red600} />}
            onPress={jest.fn()}
          />,
        );

        const primaryText = getByText('Accessible Button');
        const secondaryText = getByText('Secondary Info');

        expect(primaryText).toBeTruthy();
        expect(secondaryText).toBeTruthy();
      });

      it('works with different icon types and colors', () => {
        const iconConfigs = [
          {
            icon: <Arrow color="#FF0000" testID="red-arrow" />,
            expectedText: 'Arrow-#FF0000',
          },
          {
            icon: <Arrow color="#00FF00" testID="green-arrow" />,
            expectedText: 'Arrow-#00FF00',
          },
          { icon: <Exit testID="exit-icon" />, expectedText: 'Exit' },
        ];

        iconConfigs.forEach(({ icon, expectedText }, index) => {
          const { getByTestId, rerender } = render(
            <ProfileActionButton
              primaryText={`Button ${index}`}
              icon={icon}
              onPress={jest.fn()}
            />,
          );

          const iconElement = getByTestId(icon.props.testID);
          expect(iconElement).toHaveTextContent(expectedText);

          // Clean up for next iteration
          rerender(<></>);
        });
      });

      it('handles long text content gracefully', () => {
        const longPrimaryText =
          'This is a very long primary text that might wrap or truncate';
        const longSecondaryText =
          'This is also a very long secondary text content';

        const { getByText } = render(
          <ProfileActionButton
            primaryText={longPrimaryText}
            secondaryText={longSecondaryText}
            icon={<Arrow color={colors.red600} />}
            onPress={jest.fn()}
          />,
        );

        expect(getByText(longPrimaryText)).toBeTruthy();
        expect(getByText(longSecondaryText)).toBeTruthy();
      });

      it('maintains consistent styling across different configurations', () => {
        const configurations = [
          { primaryText: 'Config 1', icon: <Exit /> },
          {
            primaryText: 'Config 2',
            secondaryText: 'Secondary',
            icon: <Arrow color="#000" />,
          },
          { primaryText: 'Config 3', secondaryText: 'Another', icon: <Exit /> },
        ];

        configurations.forEach(config => {
          const { getByText, rerender } = render(
            <ProfileActionButton {...config} onPress={jest.fn()} />,
          );

          const primaryText = getByText(config.primaryText);
          expect(primaryText).toBeTruthy();

          if (config.secondaryText) {
            expect(getByText(config.secondaryText)).toBeTruthy();
          }

          // Clean up for next iteration
          rerender(<></>);
        });
      });
    });
  });
});
