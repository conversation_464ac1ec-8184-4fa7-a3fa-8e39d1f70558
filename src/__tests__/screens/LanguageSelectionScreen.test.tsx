import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import TestRenderer from 'react-test-renderer';
import { ScrollView } from 'react-native';
import LanguageSelectionScreen from '~/screens/LanguageSelectionScreen';

jest.mock('~/localization', () => ({
  __esModule: true,
  en: {
    select_language: 'Select Language',
    language_information: 'Choose your language',
    language_name: 'English',
    language_english: 'Default',
    flagUri: 'https://example.com/en.png',
  },
  fr: {
    language_name: 'Français',
    language_english: 'French',
    flagUri: 'https://example.com/fr.png',
  },
}));

jest.mock('@react-navigation/bottom-tabs', () => ({
  useBottomTabBarHeight: () => 20,
}));

jest.mock('~/screens/ScreenWrapper', () => {
  const React = require('react');
  const { View } = require('react-native');
  function ScreenWrapper(props: any) {
    return React.createElement(
      View,
      { testID: 'screen-wrapper', style: { backgroundColor: props.color } },
      props.children,
    );
  }
  ScreenWrapper.Body = function Body(props: any) {
    return React.createElement(
      View,
      {
        testID: 'screen-body',
        style: { padding: props.withoutPadding ? 0 : 16 },
      },
      props.children,
    );
  };
  return ScreenWrapper;
});

jest.mock('~/components/text/Title', () => {
  const React = require('react');
  const { View, Text } = require('react-native');
  return ({ title, subtitle }: any) =>
    React.createElement(
      View,
      { testID: 'title-component' },
      React.createElement(Text, null, title),
      React.createElement(Text, null, subtitle),
    );
});

jest.mock('~/components/buttons/LanguageOption', () => {
  const React = require('react');
  const { TouchableOpacity, Text } = require('react-native');
  return function MockLanguageOption({
    languageName,
    isSelected,
    onPress,
  }: {
    languageName: string;
    isSelected: boolean;
    onPress: () => void;
  }) {
    return React.createElement(
      TouchableOpacity,
      {
        testID: `language-option-${languageName}`,
        accessibilityRole: 'button',
        accessibilityState: { selected: isSelected },
        onPress,
      },
      React.createElement(Text, null, languageName),
      isSelected &&
        React.createElement(
          Text,
          { testID: `selected-${languageName}` },
          'selected',
        ),
    );
  };
});

describe('LanguageSelectionScreen', () => {
  it('renders the Title with the correct title and subtitle', () => {
    const { getByTestId, getByText } = render(<LanguageSelectionScreen />);
    expect(getByTestId('title-component')).toBeTruthy();
    expect(getByText('Select Language')).toBeTruthy();
    expect(getByText('Choose your language')).toBeTruthy();
  });

  it('renders one LanguageOption per locale entry', () => {
    const { getAllByTestId } = render(<LanguageSelectionScreen />);
    const all = getAllByTestId(/^language-option-/);
    const valid = all.filter(node => !node.props.testID.endsWith('-undefined'));
    expect(valid).toHaveLength(2);
  });

  it('selects English by default and not French', () => {
    const { getByTestId, queryByTestId } = render(<LanguageSelectionScreen />);
    expect(getByTestId('selected-English')).toBeTruthy();
    expect(queryByTestId('selected-Français')).toBeNull();
  });

  it('updates the selected language when another option is pressed', () => {
    const { getByTestId, queryByTestId } = render(<LanguageSelectionScreen />);
    fireEvent.press(getByTestId('language-option-Français'));
    expect(getByTestId('selected-Français')).toBeTruthy();
    expect(queryByTestId('selected-English')).toBeNull();
  });

  it('configures the ScrollView with the bottom tab padding and hides the indicator', () => {
    const tree = TestRenderer.create(<LanguageSelectionScreen />).root;
    const scroll = tree.findByType(ScrollView);
    expect(scroll.props.showsVerticalScrollIndicator).toBe(false);
    expect(scroll.props.contentContainerStyle).toMatchObject({
      paddingBottom: 20,
    });
  });
});
