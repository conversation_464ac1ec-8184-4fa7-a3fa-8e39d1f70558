import theme from '~/styles/theme';
import colors from '~/styles/colors';
import { Platform } from 'react-native';

const primary =
  theme.mode === 'light' ? theme.lightColors.primary : theme.darkColors.primary;

export const fontFamilyBasedOnRunningOS =
  Platform.OS === 'ios' ? 'Satoshi Variable' : 'Satoshi-Medium';

export const whiteText = {
  color: 'white',
};

export const blackText = {
  color: 'black',
};

export const blackishText = {
  color: colors.grey900,
};

export const disabledText = {
  color: colors.buttonGray,
};

export const placeholderText = {
  color: colors.transBlack60,
};

export const primaryText = {
  color: primary,
};

export const secondaryText = {
  color: colors.darkBlue700,
};

export const grayText = {
  color: colors.grey700,
};

export const errorText = {
  color: 'red',
};

export const grayishText = {
  ...letterSpacing,
  color: colors.grey600,
  fontSize: 16,
  fontFamily: fontFamilyBasedOnRunningOS,
};

export const centerText = {
  textAlign: 'center',
};

export const leftText = {
  textAlign: 'left',
};

export const h1 = {
  fontFamily: fontFamilyBasedOnRunningOS,
  fontWeight: '300',
};

export const h2 = {
  fontFamily: fontFamilyBasedOnRunningOS,
  fontWeight: '100',
};

export const h3 = {
  fontFamily: fontFamilyBasedOnRunningOS,
  fontWeight: '500',
};

export const h4 = {
  fontFamily: fontFamilyBasedOnRunningOS,
  fontWeight: '400',
};

export const h5 = {
  color: colors.grey900,
  fontFamily: fontFamilyBasedOnRunningOS,
  letterSpacing: -0.2,
  fontSize: 20,
  fontWeight: '500',
};

export const p = {
  fontFamily: fontFamilyBasedOnRunningOS,
  letterSpacing: -0.1,
  fontWeight: '400',
};

export const headerTitle = {
  fontSize: 20,
  letterSpacing: -0.2,
  fontWeight: '500',
  fontFamily: fontFamilyBasedOnRunningOS,
  color: colors.grey900,
  textAlign: 'center',
};

export const buttonText = {
  textAlign: 'center',
  fontSize: 18,
  letterSpacing: -0.1,
  fontWeight: '500',
  fontFamily: fontFamilyBasedOnRunningOS,
};

export const textWhite = {
  color: colors.white,
};

export const heading = {
  fontSize: 22,
  letterSpacing: -0.01,
  fontWeight: '500',
  fontFamily: fontFamilyBasedOnRunningOS,
  textAlign: 'center',
};

export const letterSpacing = {
  letterSpacing: -0.1,
};

export const pendingInfoText = {
  ...p,
  color: colors.darkGray,
  fontSize: 16,
};

export const cardPrimaryText = {
  ...h3,
  fontSize: 16,
  color: colors.grey900,
  fontWeight: '500',
};

export const cardSecondaryText = {
  ...grayishText,
  fontSize: 14,
  color: colors.grey600,
};

export const stepperChipText = {
  fontSize: 14,
  letterSpacing: -0.2,
  fontWeight: '500',
  fontFamily: fontFamilyBasedOnRunningOS,
  textAlign: 'center',
};

export const bottomTabLabels = {
  fontSize: 14,
  letterSpacing: 0.3,
  fontWeight: '500',
  fontFamily: fontFamilyBasedOnRunningOS,
  textAlign: 'center',
};

export const routesHeaderTitle = {
  fontFamily: fontFamilyBasedOnRunningOS,
  fontWeight: '700',
  fontSize: 26,
  color: colors.textBlack,
};

export const grayishBoldText = {
  ...letterSpacing,
  color: colors.grey400,
  fontSize: 20,
  fontFamily: fontFamilyBasedOnRunningOS,
  fontWeight: '600',
};

export const subHeaderTitle = {
  ...routesHeaderTitle,
  fontWeight: '600',
};
export const fontSize12 = {
  fontSize: 12,
};

export const fontSize16 = {
  fontSize: 16,
};

export const fontSize18 = {
  fontSize: 18,
};

export const fontSize20 = {
  fontSize: 20,
};

export const small = {
  fontSize: 14, // TODO: Make a size scale based on phone features
};

export const stepperText = {
  color: colors.darkBlue500,
  fontFamily: fontFamilyBasedOnRunningOS,
  letterSpacing: 1.5,
};
