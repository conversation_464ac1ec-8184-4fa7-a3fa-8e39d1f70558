export type AuthResponse = {
  access_token: string;
  instance_url: string;
  id: string;
  token_type: 'Bearer';
  issued_at: string;
  signature: string;
};

export interface Photos {
  picture: string;
  thumbnail: string;
}

export interface Address {}

export interface IUserProfile {
  Id: string;
  Name: string;
  Email: string;
  OwnerId: string;
  AccountId: string;
  Salesforce_Account__c: string;
  Salesforce_Account__r: ISalesforceAccount;
  // Salesforce_Account__r?: {
  //   MediumPhotoUrl?: string;
  //   LanguageLocaleKey?: string;
  // }; // TODO: update later
}

export interface ISalesforceAccount {
  MediumPhotoUrl?: string;
  LanguageLocaleKey?: string;
}

export interface Urls {
  enterprise: string;
  metadata: string;
  partner: string;
  rest: string;
  sobjects: string;
  search: string;
  query: string;
  recent: string;
  tooling_soap: string;
  tooling_rest: string;
  profile: string;
  feeds: string;
  groups: string;
  users: string;
  feed_items: string;
  feed_elements: string;
  custom_domain: string;
}

export interface UserFullInfo {
  sub: string;
  user_id: string;
  organization_id: string;
  preferred_username: string;
  nickname: string;
  name: string;
  email: string;
  email_verified: boolean;
  given_name: string;
  family_name: string;
  zoneinfo: string;
  photos: Photos;
  profile: string;
  picture: string;
  address: Address;
  is_salesforce_integration_user: boolean;
  urls: Urls;
  active: boolean;
  user_type: string;
  language: string;
  locale: string;
  utcOffset: number;
  updated_at: string;
}

export type Contact = {
  attributes: {
    type: string;
    url: string;
  };
  Id: string;
  IsDeleted: boolean;
  MasterRecordId: string | null;
  AccountId: string;
  LastName: string;
  FirstName: string;
  Salutation: string | null;
  MiddleName: string | null;
  Suffix: string | null;
  Name: string;
  RecordTypeId: string;
  OtherStreet: string | null;
  OtherCity: string | null;
  OtherState: string | null;
  OtherPostalCode: string | null;
  OtherCountry: string | null;
  OtherLatitude: number | null;
  OtherLongitude: number | null;
  OtherGeocodeAccuracy: string | null;
  OtherAddress: string | null;
  MailingStreet: string | null;
  MailingCity: string | null;
  MailingState: string | null;
  MailingPostalCode: string | null;
  MailingCountry: string | null;
  MailingLatitude: number | null;
  MailingLongitude: number | null;
  MailingGeocodeAccuracy: string | null;
  MailingAddress: string | null;
  Phone: string | null;
  Fax: string | null;
  MobilePhone: string | null;
  HomePhone: string | null;
  OtherPhone: string | null;
  AssistantPhone: string | null;
  ReportsToId: string | null;
  Email: string;
  Title: string | null;
  Department: string | null;
  AssistantName: string | null;
  LeadSource: string | null;
  Birthdate: string | null;
  Description: string | null;
  OwnerId: string;
  HasOptedOutOfEmail: boolean;
  HasOptedOutOfFax: boolean;
  DoNotCall: boolean;
  CreatedDate: string;
  CreatedById: string;
  LastModifiedDate: string;
  LastModifiedById: string;
  SystemModstamp: string;
  LastActivityDate: string | null;
  LastCURequestDate: string | null;
  LastCUUpdateDate: string | null;
  LastViewedDate: string;
  LastReferencedDate: string;
  EmailBouncedReason: string | null;
  EmailBouncedDate: string | null;
  IsEmailBounced: boolean;
  PhotoUrl: string;
  Jigsaw: string | null;
  JigsawContactId: string | null;
  IndividualId: string | null;
  MaritalStatus: string | null;
  Gender: string | null;
  DeceasedDate: string | null;
  SequenceInMultipleBirth: string | null;
  Pronouns: string | null;
  GenderIdentity: string | null;
  IsPriorityRecord: boolean;
  ContactSource: string | null;
  TitleType: string | null;
  DepartmentGroup: string | null;
  BuyerAttributes: string | null;
  Onboarding_Stage__c: string;
  DL_Front__c: boolean;
  Checkbox_Back__c: boolean;
  SS_Card__c: boolean;
  Date_of_Birth__c: string | null;
  DL_State__c: string | null;
  DL__c: string | null;
  Interview_Date__c: string | null;
  Interviewed_By__c: string | null;
  DL_Expiration__c: string | null;
  SSN__c: string | null;
  Drug_Test_Scheduled__c: string | null;
  Drug_Test_Status__c: string | null;
  Drug_Test_Result_Date__c: string | null;
  BG_Request_Date__c: string | null;
  BG_Completed_Date__c: string | null;
  Workers_Comp_Req__c: string | null;
  Work_E_mail__c: string | null;
  Work_E_Mail_Password__c: string | null;
  Submit_DL__c: string | null;
  Onboarding_Packet__c: boolean;
  ADP__c: boolean;
  Ease__c: boolean;
  E_Verify__c: boolean;
  Welcome_Page__c: boolean;
  Sexual_Harrasment__c: boolean;
  Gift_Card__c: boolean;
  Uniform__c: boolean;
  E_Mentor__c: boolean;
  Fleet_Commander__c: boolean;
  Direct_Deposit__c: boolean;
  Hiring_Packet__c: boolean;
  DL_Back__c: boolean;
  Amazon_Account__c: boolean;
  Amazon_Account_Activation__c: boolean;
  DL_Verification_Status__c: string | null;
  Training_Day_1__c: string | null;
  Training_Date_2__c: string | null;
  Passport_Style_Photo__c: boolean;
  Badge_ID__c: string | null;
  Salesforce_Account__c: string;
  Partner_Location__c: string | null;
  Company__c: string | null;
  Borrowed_Account__c: string | null;
  LID__Languages__c: string | null;
  LID__Level__c: string | null;
  LID__LinkedIn_Company_Id__c: string | null;
  LID__LinkedIn_Member_Token__c: string | null;
  LID__No_longer_at_Company__c: string | null;
  LID__Session_Id__c: string;
  Deactivate_Contact__c: boolean;
  Deactivation_Reason__c: string | null;
  Primary_Business_Contact__c: boolean;
  LSO_Username__c: string | null;
  Secondary_Business_Contact__c: boolean;
  Primary_Operations_Contact__c: boolean;
  Phone_Ext__c: string | null;
  Primary_Billing_Contact__c: boolean;
  Relationship__c: string | null;
  Branch_Wallet_Status__c: string;
  Base_Pay_Rate__c: number;
  Dispatch_Driver_Id__c: string | null;
  Direct_Deposit_Account__c: string | null;
  Insurance_Expiration_Date__c: string | null;
  Direct_Deposit_Routing__c: string | null;
  Registration_Expiration_Date__c: string | null;
  IC_DBA__c: string | null;
  Drives_Own_Vehicle__c: boolean;
  Onboarding_Paperwork__c: boolean;
  Background_Check__c: boolean;
  W9__c: boolean;
  Settlement_Rate__c: number;
  Referred_By__c: string | null;
  First_Work_Date__c: string | null;
  Referral_Bonus_Date__c: string | null;
  Referral_Status__c: string;
  Shirt_Size__c: string | null;
  Added_to_Company_Insurance__c: boolean;
  Payment_Rate_Notes__c: string | null;
  Payment_Rate__c: number | null;
  Private_Vehicle__c: string | null;
  Name_on_Bank_Account__c: string | null;
  Banking_Institution__c: string | null;
  Bank_Account_Number__c: string | null;
  Bank_Routing_Number__c: string | null;
  Tax_Classification__c: string | null;
  LLC_Tax_Classification__c: string | null;
  Tax_Classification_Other__c: string | null;
  SSN_or_EIN__c: string | null;
  EIN__c: string | null;
  City_State_and_Zip__c: string | null;
  Branch_Onboarding_Link__c: string | null;
  Full_Name__c: string;
  Disbursement_Frequency__c: string | null;
  Employee_Group__c: string | null;
  Onboarding_Documents_Signed__c: string | null;
  Schedule__c: string | null;
  Portal_User_Activated__c: boolean;
  Active_Schedule__c: boolean;
  rh2__Currency_Test__c: string | null;
  rh2__Describe__c: string | null;
  rh2__Integer_Test__c: number | null;
  rh2__Formula_Test__c: number;
  Settlement_Frequency__c: string;
  Auto_Approve_Disbursements__c: boolean;
  Instant_Pay_Withholding_Amount__c: number;
  Instant_Pay_Withholding_Balance__c: number;
  Master_Contractor__c: string | null;
  Instant_Pay_Withholding_Rate__c: number;
  Last_Route_Date__c: string | null;
  Application_Role__c: string | null;
  createCommunityUser__c: boolean;
  IsActive__c: boolean;
  Abbreviated_Name__c: string;
  Address_Confirmed__c: boolean;
  Vest_Sent__c: boolean;
  Saleforce_Account_Username__c: string;
  Temporary_Inactive_Checkbox__c: boolean;
  Age__c: number | null;
  dhcsf__DHC_Sync_Time__c: string | null;
  dhcsf__DHC_is_Active__c: boolean;
  dhcsf__Definitive_Executive_ID__c: string | null;
  dhcsf__Definitive_ID__c: string | null;
  dhcsf__From_DHC__c: boolean;
  Checked_In__c: boolean;
  Device_Type__c: string | null;
  Network_Root_Account__c: string;
  Site_Sub_Network_Root_Account__c: string | null;
  Most_Recent_Build_Version_Seen__c: string;
  Mobile_App_Garbage_Collection_Frequency__c: string | null;
  Mobile_Garbage_Collection_Frequency_Min__c: string | null;
  Mobile_Debug__c: boolean;
  Salesforce_Account_Id__c: string;
};

export interface OAuthUser {
  userId: string;
  loginUrl: string;
  communityUrl: string;
  communityId: string;
  clientId: string;
  refreshToken: string;
  orgId: string;
  accessToken: string;
  userAgent: string;
  instanceUrl: string;
}

export type UserResponse = {};
